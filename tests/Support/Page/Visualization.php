<?php

namespace Tests\Support\Page;

class Visualization {
    public static $versiumReachImage = "//img[@src='/static/media/versium-reach-dark.64263542.svg']";
    public static $listResultSuccessButton = "//button[@class='btn btn-success rounded-pill py-3']";

    // Action Buttons
    public static $shareInsightsBtn = "//button[@id='vs-list-share-button']";
    public static $downloadAsPDFBtn = "//span[@id='download-vis-cap-pdf']/button";
    public static $downloadAsPNGBtn = "//span[@id='download-vis-cap-png']/button";
    public static $PDFDownloadReadyText = 'Download this list’s visualization as PDF';
    public static $PNGDownloadReadyText = 'Download this list’s visualization as PNG';

    // Export / Buy
    public static $exportBtn = "//button[text()='Export List Preview']";
    public static $primaryResultsBtn = "//div[@id='results-main-action-btn']//button";
    public static $secondaryResultsBtn = "//div[@id='results-secondary-action-btn']//button";
    public static $exportB2cOABtn = "//button[contains(@class, 'vt-b2c-b2cOnlineAudience-results__download-button')]";
    public static $acceptTOSBtn = "//button[text()='I agree']";

    public static $exportCheckbox = "//input[@name='csv-checkbox']/following-sibling::span";
    public static $exportToGoogleCheckbox = "//input[@name='google-checkbox']/following-sibling::span";
    public static $exportToFBCheckbox = "//input[@name='fb-checkbox']/following-sibling::span";
    public static $exportToMarketingStarCheckbox = "//input[@name='marketing-star-checkbox']/following-sibling::span";
    public static $exportAgreeBtn = "//button[text()='I Agree']";
    public static $confirmExportBtn = "//button[text()='Export']";
    public static $termsOfServiceAgreeBtn = "//button[text()='I agree']";

    // Preview Modal
    public static $previewHeader = "//h1[contains(., 'List Preview')]";
    public static $closePreviewButton = "//button/span[text()='Close']";

    // Transactions Modal
    public static $CCSelectDropDown = "//label[text()='Credit Card on File']/following-sibling::select";
    public static $agreeAndPurchaseBtn = "//button[contains(text(), 'Buy for')]";
    public static $purchaseSuccessText = 'Thank you - Purchase Successful!';
    public static $exportPurchasedListBtn = "//button[text()='Export Audience']";

    // Share Insights Popover
    public static $shareInsightsHeader = "h4[text()='Share Insights']";
    public static $shareInsightsText = 'You can share these insights by sending an email';
    public static $sendInviteInput = "//input[@class='form-control text-truncate border-0 h-100']";
    public static $copyLinkInput = "//input[@name='copy_link']";
    public static $sendInviteBtn = "//button[text()='Send Invite']";
    public static $copyLinkBtn = "//button[text()='Copy Link']";
    public static $successToastText =  'Your email has been sent successfully.';
    public static $shareInsightsTooltip = "div[text()='Share Insights']";

    // Zigglio Integration Bits
    public static $zigglioSuccessString1 = 'Audience sent successfully!';
    public static $zigglioSuccessString2 = 'Your audience will appear in Facebook shortly.';
    public static $zigglioClosePopupBtn = "//button[text()='Close Window']";
    public static $zigglioUploadingIcon = "//span[@class='vs-zigglio-status-icon-container']";
    public static $zigglioInQueueText = 'In queue for Zigglio export';

    // Job Completion Strings
    //B2b
    public static $b2bOnlineAudienceFinished = "//div[text()='Online Audience Append  Results']";
    public static $b2bPersonaAudienceFinished = "//div[text()='Persona Based List  Results']";
    public static $b2bAccountBasedAudienceFinished = "//div[contains(text(),'Results')]";
    public static $b2bLookalikeAudienceFinished = 'Business Contacts Added';
    public static $b2bFirmographicAppendFinished = "//span[text()='Export']/parent::button";
    public static $ip2DomainFinished = "//span[text()='Export']/parent::button";
    public static $ip2DomainLiteFinished = "//span[text()='Export']/parent::button";
    public static $c2bAppendFinished = "//button[text()='Export as CSV']";
    // B2C
    public static $b2cContactAppendFinishedElem = "//div[contains(text(), 'Contact Append  Results')]"; //elem for Single Subscription
    public static $b2cDemoAppendFinishedElem = "//div[contains(text(), 'Demographic Append  Results')]";
    public static $b2cOnlineAudienceAppendFinishedElem = "//div[contains(text(), 'Online Audience Append  Results')]";
    public static $b2cListGenFinished = "//div[contains(text(), 'Consumer Audience Builder  Results')]";

    // B2B Card Elements
    public static $revenueCardHeader = "//h5[text()='Revenue']";
    public static $industryCardHeader = "//h5[text()='Industry']";
    public static $employeeCountCardHeader = "//h5[text()='Employee Count']";
    public static $locationCardHeader = "//h3[text()='Where these businesses are located']";
    public static $companySizeCardHeader = "//h3[text()='Company Size by Employee']";
    public static $anunualRevenueCardHeader = "//h3[text()='Company Annual Revenue']";
    public static $topIndustriesCardHeader = "//h3[text()='Top Industries']";
    // Second set of cards, lazy-loaded after the user scrolls
    public static $yearsInBusinessCardHeader = "//h3[text()='Company Years in Business']";
    public static $departmentsCardHeader = "//h3[text()='Top Departments']";
    public static $jobTitlesCardHeader = "//h3[text()='Top Job Titles']";
    public static $technologiesCardHeader = "//h3[text()='Top Technologies']";
    public static $ageAndGenderCarderHeader = "//h3[text()='Title by Age and Gender']";
    public static $companyTypeCardHeader = "//h3[text()='Company Type']";

    // B2C Card Elements
    public static $firstResultsCard = "//span[@class='vs-font-montserrat h1 text-white font-weight-bolder']";
    public static $peopleLocationsCard = "//div[@class='vs-list-info-card rounded  vs-list-info-card--with-map vt-viz-report__people-locations-card']";
    public static $maritalStatusCard = "//div[@class='vs-list-info-card rounded  vt-viz-report__top-marital-status-card']";
    public static $genderCard = "//div[@class='vs-list-info-card rounded  vt-viz-report__top-gender-card']";
    public static $childrenInHouseholdCard = "//div[@class='vs-list-info-card rounded  vt-viz-report__children-in-household-card']";
    public static $ageByGenderCard = "//div[@class='vs-list-info-card rounded  vt-viz-report__age-by-gender-card']";
    public static $generationsCard = "//div[@class='vs-list-info-card rounded  vt-viz-report__top-generation-card']";
    public static $educationCard = "//div[@class='vs-list-info-card rounded  vt-viz-report__education-card']";
    public static $householdIncomeCard = "//div[@class='vs-list-info-card rounded  vt-viz-report__household-income-card']";
    public static $ethnicitiesCard = "//div[@class='vs-list-info-card__body rounded bg-white px-2 py-3 shadow-sm ']";

    // Elements for collecting the results of Customer Test File runs
    public static $matchRateFrmtStr = "//td[text()='%s']/following-sibling::td[1]";
    public static $contactPointsFrmtStr = "//td[text()='%s']/following-sibling::td[2]";
    public static $countBoxFrmtStr = "//div[@title='%s']/following-sibling::div/span";


    private static $b2bCardHeaders1 = [];
    private static $b2bCardHeaders2 = [];
    private static $b2cCards = [];

    public static function getB2BCardHeaders1()
    {
        if (self::$b2bCardHeaders1 == null) {
            self::$b2bCardHeaders1 = [
                self::$revenueCardHeader,
                self::$industryCardHeader,
                self::$employeeCountCardHeader,
                self::$locationCardHeader,
                self::$companySizeCardHeader,
                self::$anunualRevenueCardHeader,
                self::$topIndustriesCardHeader,
            ];
        }
        return self::$b2bCardHeaders1;
    }

    public static function getB2BCardHeaders2()
    {
        if (self::$b2bCardHeaders2 == null) {
            self::$b2bCardHeaders2 = [
                self::$yearsInBusinessCardHeader,
                self::$departmentsCardHeader,
                self::$jobTitlesCardHeader,
                self::$technologiesCardHeader,
                self::$ageAndGenderCarderHeader,
                self::$companyTypeCardHeader,
            ];
        }
        return self::$b2bCardHeaders2;
    }

    public static function getB2CCards()
    {
        if (self::$b2cCards == null) {
            self::$b2cCards = [
                self::$peopleLocationsCard,
                self::$maritalStatusCard,
                self::$genderCard,
                self::$childrenInHouseholdCard,
                self::$ageByGenderCard,
                self::$generationsCard,
                self::$educationCard,
                self::$householdIncomeCard,
                self::$ethnicitiesCard
            ];
        }
        return self::$b2cCards;
    }
}