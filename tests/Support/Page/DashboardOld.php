<?php

namespace Tests\Support\Page;


class DashboardOld
{
    # Old dashboard, pre-Marketing Star


    # Header
    public static $versiumReachImage = "//img[@src='/static/media/versium-reach-light.1cba2388.svg']";
    public static $homeLink = "//a[@href='/']";
    # Account Settings
    public static $accountSettingsButton = "//nav//button";
    public static $accountSettingsLink = "//a[@href='/account']";
    public static $adminUsersLink = "//a[@href='/admin-users']";
    public static $logoutText = 'Log Out';
    # Account Info
    public static $accountInfoTitle = 'Account Info';
    public static $accountInfoStart = 'Contract Start Date';
    public static $accountInfoEnd = 'Next Billing Date';
    public static $accountInfoMoreCredits = 'More Match Credits on';
    public static $accountInfoCreditsAvailable = 'Match Credits Available';

    # Dashboard
    public static $headerMessage = 'What would you like to do?';
    # Action Tabs
    public static $consumerToolsTab = "//a[text()='Consumer Data']";
    public static $businessToolsTab = "//a[text()='Business Data']";
    public static $dataHygieneTab = "//a[text()='Data Hygiene']";
    # Card Links
    public static $listInsightsCard = "//a[@href='/projects/create/list-insights']";
    public static $personaListCard = "//a[@href='/projects/create/persona-list']";
    public static $abmTargetedListCard = "//a[@href='/projects/create/abm-list']";
    public static $lookAlikeListCard = "//a[@href='/projects/create/look-alike-list']";
    public static $onlineAudienceCard = "//a[@href='/projects/create/online-audience']";
    public static $firmographicCard = "//a[@href='/projects/create/firmographic-append']";
    public static $ip2DomainCard = "//a[@href='/projects/create/ip-to-domain']";
    public static $ip2DomainLiteCard = "//a[@href='/projects/create/ip-to-domain-lite']";
    public static $c2bAppendCard = "//a[@href='/projects/create/c2b-append']";
    # Consumer Data Tools
    public static $contactAppendCard = "//a[contains(@href, '/projects/create/contact-append')]";
    public static $demographicAppendCard = "//a[@href='/projects/create/demographic-append']";
    public static $b2cOnlineAudienceCard = "//a[@href='/projects/create/b2c-online-audience']";
    public static $b2cListInsightsCard = "//a[@href='/projects/create/b2c-list-insights']";
    public static $listGenCard = "//a[@href='/projects/create/b2c-people-list']";
    # Data Hygiene Tools
    public static $dataPrepCard = "//a[@href='/projects/create/data-workbench']";

    # Versium Announcements
    public static $announcementsHeader = "//h3[text()='Versium Announcements']";
    public static $announcementsSubHeader1 = "//h4[text()='Now Available Free: Dedupe Your Audience Data!']";
    public static $announcementsSubHeader2 = "//h4[text()='ABM & Persona based List Generation now includes Business Person Emails']";
    public static $announcementsSubHeader3 = "//h4[text()='Try our updated Consumer Audience Builder!']";

    # Quick Resources
    public static $quickResourcesHeader = 'Quick Resources';
    public static $consumerDataToolsLink = "//a[@href='https://reach-help.versium.com/docs/introduction-to-consumer-data-tools']";
    public static $businessDataToolsLink = "//a[@href='https://reach-help.versium.com/docs/introduction-to-business-data-tools']";
    public static $connectToHubspotLink = "//a[@href='https://reach-help.versium.com/docs/connect-a-hubspot-account-to-versium-reach']";
    public static $connectToFacebookLink = "//a[@href='https://reach-help.versium.com/docs/facebook-campaigns']";
    public static $connectToGoogleLink = "//a[@href='https://reach-help.versium.com/docs/google-ads-campaigns']";

    # Footer
        # Elements
    public static $appVersionElement = "//span[@id='vs-app-version']";
    public static $appVersionToast = "//div[@class='Toastify__toast Toastify__toast-theme--colored Toastify__toast--info vs-app__version-toast border border-secondary text-black shadow-sm px-3 pb-4']";
        # Strings
    public static $appVersion = 'V6.5.1';
    public static $appVersionText = 'Versar Edition';

    # Trial End
    public static $trialEndBar = "//div[@class='vs-trial-upgrade-bar font-weight-bolder text-white text-center d-flex flex-column bg-success']";
    public static $viewOptionsBtn = "//button[@class='vs-trial-upgrade-bar__upgrade-button bg-transparent border-0 p-0 text-white font-weight-bolder shadow-none']";
    public static $trialModalHeaderText = 'Looks like you want to upgrade';
    public static $enterCardDetailsBtn = "//button[text()='Choose Pay as You Go']";
    public static $contactSalesBtn = "//button[text()='Contact Sales']";
    public static $contactSalesSuccessText = 'Thank you for choosing Versium REACH';
    public static $contactSalesSuccessCloseBtn = "button[text()='You can close this window']";

    # Usage Snapshot Widget
    public static $usageSnaphotTitle = 'Usage Snapshot';
    public static $usageSnapshotBillingPeriod = 'Your usage for the current billing period';
    public static $usageSnapshotSVG = "//*[local-name()='svg' and @height='200px' and @width='200px']";


    private static $consumerOpsAndBreadcrumbs = null;
    private static $businessOpsAndBreadcrumbs = null;
    private static $dataHygieneAndBreadcrumbs = null;
    private static $accountInfoStrings = null;
    private static $announcementElems = null;
    private static $quickResourcesLinks = null;

    public static function consumerOpsAndBreadcrumbs() {
        if (self::$consumerOpsAndBreadcrumbs == null) {
            self::$consumerOpsAndBreadcrumbs = [
                # b2c
                self::$b2cListInsightsCard => 'Create List Insights',
                self::$contactAppendCard => 'Create a Contact Append',
                self::$demographicAppendCard => 'Create a Demographic Append',
                self::$b2cOnlineAudienceCard => 'Create a B2C Online Audience',
                self::$listGenCard => 'Choose Filters'
            ];
        }
        return self::$consumerOpsAndBreadcrumbs;
    }

    public static function bussinessOpsAndBreadcrumbs() {
        if (self::$businessOpsAndBreadcrumbs == null) {
            self::$businessOpsAndBreadcrumbs = [
                # b2b
                self::$listInsightsCard => 'Create List Insights',
                self::$personaListCard => 'Create a Persona List',
                self::$abmTargetedListCard => 'Create an Account-based Audience',
                self::$lookAlikeListCard => 'Create a Look-alike List',
                self::$onlineAudienceCard => 'Create a B2B Online Audience',
                self::$firmographicCard => 'Create a Firmographic Append',
                self::$ip2DomainCard => 'Create IP to Domain',
                // self::$ip2DomainLiteCard => 'Create IP to Domain Lite',
                self::$c2bAppendCard => 'Create a C2B Append',
            ];
        }
        return self::$businessOpsAndBreadcrumbs;
    }

    public static function dataHygieneAndBreadcrumbs() {
        if (self::$dataHygieneAndBreadcrumbs == null) {
            self::$dataHygieneAndBreadcrumbs = [
                self::$dataPrepCard => 'Data Prep'
            ];
        }
        return self::$dataHygieneAndBreadcrumbs;
    }

    public static function getAccountInfoStrings()
    {
        if (self::$accountInfoStrings == null) {
            self::$accountInfoStrings = [
                self::$accountInfoTitle,
                self::$accountInfoStart,
                self::$accountInfoEnd,
                self::$accountInfoMoreCredits,
                self::$accountInfoCreditsAvailable,
            ];
        }
        return self::$accountInfoStrings;
    }

    public static function getAnnouncementElems()
    {
        if (self::$announcementElems == null) {
            self::$announcementElems = [
                self::$announcementsHeader,
                self::$announcementsSubHeader1,
                self::$announcementsSubHeader2,
                self::$announcementsSubHeader3,
            ];
        }
        return self::$announcementElems;
    }

    public static function getQuickResourcesElems()
    {
        if (self::$quickResourcesLinks == null) {
            self::$quickResourcesLinks = [
                self::$consumerDataToolsLink,
                self::$businessDataToolsLink,
                self::$connectToHubspotLink,
                self::$connectToFacebookLink,
                self::$connectToGoogleLink,
            ];
        }
        return self::$quickResourcesLinks;
    }
}
