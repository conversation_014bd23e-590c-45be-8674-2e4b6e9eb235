<?php

namespace Tests\Support\Page;

class ProjectsList {
    public static $createProjectButton = "//button[contains(.,'Create Project')]";
    public static $homeLink = "//a[contains(.,'Home')]";
    public static $projectLocatorFormatString = "//a[contains(text(),'%s')]";
    public static $createProjectText = "Enter a name for your project.";
    public static $closeButton = "//button[@class='close']";
    public static $createButton = "//button[contains(text(),'Create')]";
    public static $cancelButton = "//button[contains(text(),'Cancel')]";
    public static $newProjectInput = "//input[@name='campaignName']";

    public static $searchInput = "//input[@type='search']";
    public static $finalDeleteButton = "//button[@class='btn btn-danger rounded-pill' and contains(text(),'Delete')]";
    public static $projectNameLocator = '//tbody/tr/*[3]/a';

    public static $sortIconLocatorString = "//th[@data-sort-key='%s']";
    // sort keys
    public static $nameSortKey = 'name';
    public static $listsProcessingSortKey = 'lists_processing_count';
    public static $listsCountSortKey = 'lists_count';
    public static $createdAtSortKey = 'created_at';

    public static $fieldNameLocatorFormatString = '//tbody/tr/*[%d]';

    // Table Pagination
    public static $resultsTextElem = "//span[text()='Results per page:']";
    public static $paginationDropdown = "//div[@class='btn-group']";
    public static $show10PaginationBtn = "//button[text()='10']";
    public static $show25PaginationBtn = "//button[text()='25']";
    public static $nxtPageBtn = "//*[local-name()='svg' and @data-icon='arrow-right']//parent::a";
    public static $previousPageBtn = "//*[local-name()='svg' and @data-icon='arrow-left']//parent::a";
    public static $page1LinkActive = "//a[@class='page-link number-link rounded border-primary text-primary bg-transparent' and text()='1']";
    public static $page2LinkActive = "//a[@class='page-link number-link rounded border-primary text-primary bg-transparent' and text()='2']";


    private static $columnIndexMap = null;

    public static function getColumnIndexMap() {
        if (self::$columnIndexMap == null) {
            self::$columnIndexMap = [
                self::$nameSortKey            => 3,
                self::$listsProcessingSortKey => 5,
                self::$listsCountSortKey      => 6,
                self::$createdAtSortKey       => 7,
            ];
        }

        return self::$columnIndexMap;
    }
}