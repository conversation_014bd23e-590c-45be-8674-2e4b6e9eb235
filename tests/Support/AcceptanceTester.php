<?php

namespace Tests\Support;

/**
 * Inherited Methods
 * @method void wantToTest($text)
 * @method void wantTo($text)
 * @method void execute($callable)
 * @method void expectTo($prediction)
 * @method void expect($prediction)
 * @method void amGoingTo($argumentation)
 * @method void am($role)
 * @method void lookForwardTo($achieveValue)
 * @method void comment($description)
 * @method \Codeception\Lib\Friend haveFriend($name, $actorClass = null)
 *
 * @SuppressWarnings(PHPMD)
 */

use Tests\Support\Enums\ImportListType;
use Tests\Support\Enums\ProjectType;
use Tests\Support\Enums\MatchType;
use Tests\Support\Enums\JobType;
use Tests\Support\Enums\ListAction;

use Tests\Support\Page\B2CAttributeSelection as AttributeSelectPage;
use Tests\Support\Page\ChooseProject as ChooseProjectPage;
// use Tests\Support\Page\Dashboard as DashboardPage;
use Tests\Support\Page\DashboardOld as DashboardPage;
use Tests\Support\Page\ListInsights as ListInsightsPage;
use Tests\Support\Page\Login as LoginPage;
use Tests\Support\Page\Persona as PersonaPage;
use Tests\Support\Page\Project as ProjectPage;
use Tests\Support\Page\ProjectsList as ProjectsListPage;
use Tests\Support\Page\Visualization as VisualizationPage;
use Tests\Support\Page\SideNav;
use Tests\Support\Page\B2CListGen as ListGenPage;
use Tests\Support\Page\DataPrep;
use Tests\Support\Page\EmailValidation as EmailValidationPage;

use Tests\Support\Classes\ListGenGeoFilters;
use Tests\Support\Classes\ListGenDemoFilters;
use Tests\Support\Classes\ListGenCustomLimits;
use Tests\Support\Classes\ListGenAttrConfig;

use Tests\Support\Enums\ContactType;

class AcceptanceTester extends \Codeception\Actor {
    use _generated\AcceptanceTesterActions;

    const B2B = 'b2b';
    const B2C = 'b2c';

    const HUBSPOT_LIST_NAME = 'Customer Survey Responders (29 records).csv';


    public function login($email, $password, $marketingStarDashboard = false)
    {
        $I = $this;

        $I->amOnPage(LoginPage::$URL);

        # set cookie to prevent alert banner
        $I->setAlertBannerCookie();
        # set localStorage to prevent marketing star promo
        $I->setMarketingStarModalCookie();

        $I->waitForElement(LoginPage::$emailField);

        $I->fillField(LoginPage::$emailField, $email);
        $I->fillField(LoginPage::$passwordField, $password);
        $I->waitForElementAndClick(LoginPage::$logInButton);

        $I->waitForElement("//span[text()='Home']");

        # redirect to new M* dashboard
        if ($marketingStarDashboard) {
            $I->amOnPage('/?mStarPromo');
            $I->wait(2);
        }
    }

    public function logout()
    {
        $I = $this;

        $I->waitForElementAndClick(DashboardPage::$accountSettingsButton);
        $I->waitForText(DashboardPage::$logoutText);

        $I->waitForElementAndClick("//a[@href='/logout']");
        $I->wait(1);

        // once signed out, verify we are back on sign in page
        // $I->waitForElement("//h3[text()='Log In']");
    }

    public function waitForElementAndClick($element, $timeout = 60)
    {
        $I = $this;

        $I->waitForElement($element, $timeout);
        $I->click($element);
    }

    public function waitForTextAndClick($text)
    {
        $I = $this;

        $I->waitForText($text, 60);
        $I->click($text);

    }

    public function isArraySorted(array $array, string $order = 'asc', bool $numeric = false): bool {
        $length = count($array);

        # An array with 0 or 1 elements is considered sorted
        if ($length < 2) {
            return true;
        }

        for ($i = 0; $i < $length - 1; $i++) {
            if ($numeric) {
                # Numeric comparison
                $current = (float) $array[$i];
                $next = (float) $array[$i + 1];

                if ($order === 'asc') {
                    if ($current > $next) {
                        return false;
                    }
                } elseif ($order === 'desc') {
                    if ($current < $next) {
                        codecept_debug("Not sorted in descending order. Element $i: " . $array[$i] . " ($current), Element " . ($i + 1) . ": " . $array[$i + 1] . " ($next)");
                        return false;
                    }
                }
            } else {
                # String comparison
                if ($order === 'asc') {
                    # Cast to lowercase and use strcmp for strict string comparison
                    if (strcmp(strtolower($array[$i]), strtolower($array[$i + 1])) > 0) {
                        # Not sorted in ascending order
                        return false;
                    }
                } elseif ($order === 'desc') {
                    if (strcmp(strtolower($array[$i]), strtolower($array[$i + 1])) < 0) {
                        # Not sorted in descending order
                        return false;
                    }
                }
            }

            if ($order !== 'asc' && $order !== 'desc') {
                codecept_debug("Invalid sort order: $order. Use 'asc' or 'desc'.");
                return false;
            }
        }

        # The array is sorted according to the specified order
        return true;
    }

    public function canSeeStrings($strings)
    {
        $I = $this;

        foreach ($strings as $string) {
            $I->canSee($string);
        }
    }

    public function canSeeElements(array $elements)
    {
        $I = $this;

        foreach ($elements as $element) {
            $I->canSeeElement($element);
        }
    }

    public function canSeeElementsInDOM(array $elements)
    {
        $I = $this;

        foreach ($elements as $element) {
            $I->canSeeElementInDOM($element);
        }
    }

    public function canSeeLinks($links)
    {
        $I = $this;

        foreach ($links as $link) {
            if ( isset($link[0]) && isset($link[1]) ) {
                $I->canSeeLink($link[0], $link[1]);
            } else {
                $I->canSeeLink($link[0]);
            }
        }
    }

    /**
     * Navigates to projects page; If name is passed, it opens that specific project
     */
    public function openProject($projectName = null)
    {
        $I = $this;

        $I->waitForElementAndClick(SideNav::$projectsLink);
        $I->seeCurrentUrlEquals('/projects');
        $I->wait(1);

        if ($projectName) {
            $I->waitForElementAndClick(sprintf(ProjectsListPage::$projectLocatorFormatString, $projectName));
            $I->waitForElement(ProjectPage::$projectHeaderMessage);
        }
    }

    /**
     * Creates a new project
     */
    public function createProject(string $projectName)
    {
        $I = $this;

        $I->openProject();

        $I->waitForElementAndClick(ProjectsListPage::$createProjectButton);
        $I->waitForText('Enter a name for your project.');
        $I->fillField(ProjectsListPage::$newProjectInput, $projectName);
        $I->waitForElementAndClick(ProjectsListPage::$createButton);
        $I->wait(1);
    }

    public function openList($name, $scrollLists = false)
    {
        $I = $this;

        if ($scrollLists) {
            $I->scrollProjectLists();
        }

        $I->waitForElementAndClick(sprintf(ProjectPage::$listLocatorFormatString, $name));

    }

    public function openListFromProject($projectName, $listName, $scrollLists = false) {
        $I = $this;

        $I->openProject($projectName);
        $I->openList($listName, $scrollLists);
    }

    public function openListActionMenu($projectName, $listName) {
        $I = $this;

        $I->openListFromProject($projectName, $listName);

        $listNameElem = sprintf(ProjectPage::$listLocatorFormatString, $listName);
        $I->assertEquals($listName, $I->grabTextFrom($listNameElem),
            "Displayed list name is not same as uploaded list name");

        $listActionButton = $listNameElem . '/parent::div/parent::div/parent::a/following-sibling::button';

        // open actions menu
        $I->click($listActionButton);
        $I->wait(2);
    }

    public function doListAction($projectName, $listName, $action) {
        $I = $this;

        $I->openListActionMenu($projectName, $listName);

        $actionButtonString = '';
        switch ($action) {
            case JobType::LookalikeAudience:
                $actionButtonString = 'vs-look-alike-button';
                break;
            case JobType::AccountBasedAudience:
                $actionButtonString = 'vs-abm-button';
                break;
            case JobType::B2bOnlineAudience:
                $actionButtonString = 'vs-b2b-online-extenstion-button';
                break;

            case ListAction::Export:
                $actionButtonString = 'vs-list-action__download';
                break;
            case ListAction::Preview:
                $actionButtonString = 'vs-list-action__preview';
                break;
            case ListAction::Rename:
                $actionButtonString = 'vs-list-action__rename';
                break;
            case ListAction::Delete:
                $actionButtonString = 'vs-list-action__delete';
                break;
        }

        $actionButtonLocatorFormatString = "//button[@id='%s']";
        $actionButton = sprintf($actionButtonLocatorFormatString, $actionButtonString);

        $I->waitForElementAndClick($actionButton, 1800);
        $I->wait(2);
    }

    public function doB2CListAction($projectName, $listName, $action)
    {
        $I = $this;

        $I->openListActionMenu($projectName, $listName);

        $actionButtonString = '';
        switch ($action) {
            case JobType::ContactAppend:
                // matches Contact Append AND Contact Append Plus
                $actionButtonString = 'vs-b2c-contact-append-';
                break;
            case JobType::DemographicAppend:
                $actionButtonString = 'vs-b2c-demo-append-button';
                break;
            case JobType::B2cOnlineAudience:
                $actionButtonString = 'vs-b2c-online-audience-button';
                break;

            case ListAction::Export:
                $actionButtonString = 'vs-list-action__download';
                break;
            case ListAction::Preview:
                $actionButtonString = 'vs-list-action__preview';
                break;
            case ListAction::Rename:
                $actionButtonString = 'vs-list-action__rename';
                break;
            case ListAction::Delete:
                $actionButtonString = 'vs-list-action__delete';
                break;
        }

        $actionButtonLocatorFormatString = "//button[contains(@id, '%s')]";
        $actionButton = sprintf($actionButtonLocatorFormatString, $actionButtonString);

        $I->waitForElementAndClick($actionButton, 1800);
        $I->wait(2);
    }

    /*******************************************************************************************/
    //                                     JOB FLOWS
    /*******************************************************************************************/

    public function doLookAlikeListFlow(
        ImportListType $listImportType,
        $listName,
        $projectName,
        ProjectType $projectType
    )
    {
        $I = $this;
        $I->importListStep($listImportType, $listName, $projectName);

        $I->waitForElement("//h4[text()='Sampling your list... This may take a few minutes.']");
        $I->waitForText("Choose attributes for ‘Strict Match’", 600); // Sampling can take a looong time
        $I->waitForElementAndClick(ListInsightsPage::$lookAlikeAttributeProcessorNextStepButton);

        $I->chooseProjectStep($projectType, $projectName);
    }

    public function doABMListFlow(
        ImportListType $listImportType,
        $listName,
        $projectName,
        ProjectType $projectType,
        $campaignType = null,
    )
    {
        $I = $this;
        $I->importListStep($listImportType, $listName, $projectName);

        # wait for estimated contacts to stabilize
        $I->wait(5);
        # select Campaign Type
        if ($campaignType === 'email') {
            $I->waitForElementAndClick("//label[text()='Direct Email']");
        } else if ($campaignType === 'omnichannel') {
            $I->waitForElementAndClick("//label[text()='Omnichannel Marketing']");
        }
        $I->scrollB2CListGen(0, 300);
        # open Title Seniority
        $I->waitForElementAndClick("//button[@id='d_titlerank']");
        $I->wait(1);
        # Owner President
        $I->waitForElementAndClick("//label[@class='vs-checkbox position-relative d-flex align-items-center vs-cursor-pointer mb-0']");
        $I->waitForElementAndClick("//button[@id='d_titlerank']"); // close Title Seniority
        $I->fillField("//input[@name='cfg_maxrecsperdomain']", 100);
        $I->waitForElement("//div[@class='table-responsive border-top mt-1']", 180);
        $I->wait(5); // wait till the estimated contacts stabilize
        $I->scrollTo(ListInsightsPage::$abmFilterNextStepButton);
        $I->waitForElementAndClick(ListInsightsPage::$abmFilterNextStepButton);

        # we show this modal for 'email' & 'omnichannel' campaign types
        $authorizeDeduction = $campaignType ? true : false;

        $I->chooseProjectStep($projectType, $projectName, $authorizeDeduction);
    }

    public function doListInsightsFlow(
        ImportListType $listImportType,
        $listName,
        $projectName,
        ProjectType $projectType,
    )
    {
        $I = $this;
        $I->importListStep($listImportType, $listName, $projectName);

        $I->wait(4); // firmographic/ip2dom have a longer wait time on the "Saving your mapped fields" step

        $I->chooseProjectStep($projectType, $projectName);
    }

    public function doB2cContactAppend(
        ImportListType $listImportType,
        $listName,
        $projectName,
        $appends,
        ProjectType $projectType,
        $phoneAppendType = null,
        MatchType $matchType = null,
    )
    {
        $I = $this;
        $I->importListStep($listImportType, $listName, $projectName);

        $I->waitForText(AttributeSelectPage::$sideHeaderString);
        // select matchType
        if ($matchType) {
            if ($matchType === MatchType::HouseHold) {
                $I->waitForElementAndClick(AttributeSelectPage::$matchTypeLabelHH);
            } else {
                $I->waitForElementAndClick(AttributeSelectPage::$matchTypeLabelINDIV);
            }
        }
        // select append types
        foreach ($appends as $appendTypeLabel) {
            $I->waitForElementAndClick($appendTypeLabel);
        }
        // select phoneAppendType
        if ($phoneAppendType) {
            $I->waitForElementAndClick($phoneAppendType);
        }
        // wait for matches to stablize and proceed
        $I->wait(3);
        $I->waitForElementAndClick(AttributeSelectPage::$nextStepBtn);

        $I->chooseProjectStep($projectType, $projectName);
    }

    public function doB2cDemographicAppend(
        ImportListType $listImportType,
        $listName,
        $projectName,
        $appendTypes,
        ProjectType $projectType,
    )
    {
        $I = $this;
        $I->importListStep($listImportType, $listName, $projectName);

        $I->waitForText(AttributeSelectPage::$sideHeaderString, 600);
        // select append types
        foreach ($appendTypes as $appendType) {
            $I->waitForElementAndClick($appendType);
        }
        // wait for matches to stablize and proceed
        $I->wait(3);
        $I->waitForElementAndClick(AttributeSelectPage::$nextStepBtn);

        $I->chooseProjectStep($projectType, $projectName);
    }

    public function doC2bAppend(
        ImportListType $listImportType,
        $listName,
        $projectName,
        ProjectType $projectType,
        bool $excludeFields = false
    )
    {
        $I = $this;
        $I->importListStep($listImportType, $listName, $projectName, $excludeFields);

        // wait for records to stabalize and proceed
        $I->waitForElement("//h2[text()='Consumer to Business']", 600); // "Saving your mapped fields..."; takes forever b/c optout is > 1mil recs
        $I->wait(3);
        $I->waitForElementAndClick("//span[@id='vs-filters-and-preview__next-step']");

        $I->chooseProjectStep($projectType, $projectName);
    }

    public function doPersonaListFlow(
        $listName,
        $projectName,
        ProjectType $projectType,
        // filters
        $industries = [],
        $titleSeniority = [],
        $campaignType = null,
        $usStates = []
    )
    {
        $I = $this;

        $I->waitForElement(PersonaPage::$filterHeader);

        // Select Campaign Type
        if ($campaignType === 'email') {
            $I->waitForElementAndClick("//label[text()='Direct Email']");
        } else if ($campaignType === 'omnichannel') {
            $I->waitForElementAndClick("//label[text()='Omnichannel Marketing']");
        }
        // Industries Filter
        if (!empty($industries)) {
            $I->waitForElementAndClick(PersonaPage::$industriesBtn);
            $I->waitForText(PersonaPage::$industriesTitle);
            foreach ($industries as $industry) {
                $I->checkOption(sprintf(PersonaPage::$industryFormatString, $industry));
            }
            $I->click(PersonaPage::$industriesBtn);
        }
        // Title Seniority Filter
        if (!empty($titleSeniority)) {
            $I->waitForElementAndClick(PersonaPage::$titleSeniorityButton);
            $I->wait(1);
            foreach ($titleSeniority as $ts) {
                $I->checkOption(sprintf(PersonaPage::$titleSeniorityFormatString, $ts));
            }
            $I->click(PersonaPage::$titleSeniorityButton);
        }
        // Location Filters
        if (!empty($usStates)) {
            $I->waitForElementAndClick(PersonaPage::$usStatesBtn);
            $I->waitForElement(PersonaPage::$usStatesTitle);
            foreach ($usStates as $state) {
                if ($I->getBrowser() === 'safari') {
                    $I->wait(2); # safari chokes w/o wait
                }
                $I->checkOption(sprintf(PersonaPage::$usStatesFormatString, $state));
            }
            $I->click(PersonaPage::$usStatesBtn);
        }
        // Waiting for matches to stabalize
        $I->waitForElementAndClick(PersonaPage::$nextStepButton);
        // Enter ListName
        $I->waitForElement(PersonaPage::$listNameInput);
        $I->fillField(PersonaPage::$listNameInput, $listName);

        // we show this modal for 'email' & 'omnichannel' campaign types
        $authorizeDeduction = $campaignType ? true : false;

        $I->chooseProjectStep($projectType, $projectName, $authorizeDeduction);
    }

    public function doB2cListGenFlow(
        string $listName,
        string $projectName,
        ProjectType $projectType,
        array $contactFilters,
        ?ListGenCustomLimits $customLimits,
        ListGenGeoFilters $geoFilters,
        ?ListGenDemoFilters $demoFilters,
        ?ListGenAttrConfig $attrConfig
    )
    {
        $I = $this;

        // 0. Intro Step
        $headerText = $I->grabTextFrom(ListGenPage::$step1Header);
        if ($headerText === 'What is Consumer Audience Builder?') {
            $I->waitForElementAndClick(ListGenPage::$skipIntroLabel);
            $I->waitForElementAndClick(ListGenPage::$nextStepBtn);
        }

        $I->wait(1);

        // 1. Contact Info
        $I->seeElement(ListGenPage::$filtersPageTitle);
        foreach($contactFilters as $filter) {
            switch($filter->contactType) {
                case ContactType::Address:
                    $I->waitForElementAndClick(ListGenPage::$includeAddrToggle);
                    break;
                case ContactType::Phone:
                    $I->waitForElementAndClick(ListGenPage::$includePhoneToggle);
                    if ($filter->availability === 'When Available') {
                        $I->waitForElementAndClick("//div[@id='availability-options-phone']");
                        $I->waitForElementAndClick("//div[text()='When Available']");
                    }
                    break;
                case ContactType::Email:
                    $I->waitForElementAndClick(ListGenPage::$includeEmailToggle);
                    break;
                case ContactType::OnlineAudience:
                    $I->waitForElementAndClick(ListGenPage::$includeOAToggle);
                    break;
            }
        }

        $I->click(ListGenPage::$applyBtn);

        // 2. Customize Limits
        if ($customLimits) {
            match($customLimits->householdType) {
                MatchType::Indiv => $I->click(ListGenPage::$indivRadio),
                default => $I->click(ListGenPage::$hhldRadio),
            };
            if ($customLimits->recordLimit) {
                $I->click(ListGenPage::$maxRecToggleLabel);
                $I->wait(1);
                $I->fillField(ListGenPage::$maxRecInputField, $customLimits->recordLimit);
            }
            if ($customLimits->skipEmailValidation) {
                $I->waitForElementAndClick(ListGenPage::$skipEmailValidationLabel);
            }
        }
        $I->click(ListGenPage::$applyBtn);

        // 3. Geography
        if ($geoFilters->city) {
            $I->waitForElementAndClick(ListGenPage::$cityRadio);
            foreach($geoFilters->city as $city) {
                $I->fillField(ListGenPage::$cityInputField, $city);
                $I->waitForElementAndClick(sprintf(ListGenPage::$geoFilterFormatString, $city));
            }
        }
        if ($geoFilters->county) {
            $I->waitForElementAndClick(ListGenPage::$countyRadio);
            foreach($geoFilters->county as $county) {
                $I->fillField(ListGenPage::$countyInputField, $county);
                $I->waitForElementAndClick(sprintf(ListGenPage::$geoFilterFormatString, $county));
            }
        }
        if ($geoFilters->state) {
            $I->waitForElementAndClick(ListGenPage::$stateRadio);
            foreach($geoFilters->state as $state) {
                $I->fillField(ListGenPage::$stateInputField, $state);
                $I->waitForElementAndClick(sprintf(ListGenPage::$geoFilterFormatString, $state));
            }
        }
        if ($geoFilters->zip) {
            $I->waitForElementAndClick(ListGenPage::$zipRadio);
            $zips = implode(",", $geoFilters->zip);
            $I->fillField(ListGenPage::$zipInputField, $zips);
        }
        $I->click(ListGenPage::$applyBtn);
        $I->wait(2);

        // 4. Demographic
        if (isset($demoFilters)) {
            if ($demoFilters->demographic) {
                $I->click(ListGenPage::$demographicBtn);
                $I->wait(1);
                foreach ($demoFilters->demographic as [$filter, $subOption]) {
                    // click the radio, based on filter
                    $I->click(sprintf(ListGenPage::$demoFilterRadioFormatString, $filter));
                    $I->wait(1);

                    // Slider filter
                    if (getType($subOption) === 'array') {
                        [$rightClicks, $leftClicks] = $subOption;

                        $I->waitForElementAndClick(sprintf(ListGenPage::$leftSliderHandleFormatString, $filter));
                        for ($i  = 0; $i < $rightClicks; $i++) {
                            $I->sendKeys(\Facebook\WebDriver\WebDriverKeys::RIGHT);
                        }
                        $I->wait(1);

                        $I->waitForElementAndClick(sprintf(ListGenPage::$rightSliderHandleFormatString, $filter));
                        for ($i  = 0; $i < $leftClicks; $i++) {
                            $I->sendKeys(\Facebook\WebDriver\WebDriverKeys::LEFT);
                        }
                    // Label Filter
                    } else {
                        $I->click(sprintf(ListGenPage::$demoFilterLabelFormatString, $subOption));
                        $I->wait(1);
                    }
                }
                $I->click(ListGenPage::$applyBtn);
                $I->wait(1);
            }
            if ($demoFilters->houseFinAuto) {
                $I->click(ListGenPage::$finAutoBtn);
                $I->wait(1);
                foreach ($demoFilters->houseFinAuto as [$filter, $subOption]) {
                    // click the radio, based on filter
                    $I->click(sprintf(ListGenPage::$finAutoFilterRadioFormatString, $filter));
                    $I->wait(1);

                    // Slider filter
                    if (getType($subOption) === 'array') {
                        [$rightClicks, $leftClicks] = $subOption;

                        $I->waitForElementAndClick(sprintf(ListGenPage::$leftSliderHandleFormatString, $filter));
                        for ($i  = 0; $i < $rightClicks; $i++) {
                            $I->sendKeys(\Facebook\WebDriver\WebDriverKeys::RIGHT);
                        }
                        $I->wait(1);

                        $I->waitForElementAndClick(sprintf(ListGenPage::$rightSliderHandleFormatString, $filter));
                        for ($i  = 0; $i < $leftClicks; $i++) {
                            $I->sendKeys(\Facebook\WebDriver\WebDriverKeys::LEFT);
                        }

                    // Label Filter
                    } else {
                        $I->click(sprintf(ListGenPage::$finAutoFilterLabelFormatString, $subOption));
                        $I->wait(1);
                    }
                }
                $I->click(ListGenPage::$applyBtn);
                $I->wait(1);
            }
            if ($demoFilters->lifestyleInterest) {
                $I->click(ListGenPage::$lifestyleBtn);
                $I->wait(1);
                foreach ($demoFilters->lifestyleInterest as $filter) {
                    $I->click(sprintf(ListGenPage::$lifestyleFilterLabelFormatString, $filter));
                    $I->wait(1);
                }
                $I->click(ListGenPage::$applyBtn);
                $I->wait(1);
            }
            if ($demoFilters->politicalDonor) {
                $I->click(ListGenPage::$politicalBtn);
                $I->wait(1);
                foreach ($demoFilters->politicalDonor as [$filter]) {
                    $I->click(sprintf(ListGenPage::$politicalFilterLabelFormatString, $filter));
                    $I->wait(1);
                }
                $I->click(ListGenPage::$applyBtn);
                $I->wait(1);
            }
        }

        $I->click(ListGenPage::$nextStepBtn);
        $I->wait(2);

        // 5. Demo Attributes
        if ($attrConfig) {
            if ($attrConfig->includeAttrs === true) {
                $I->waitForElementAndClick(ListGenPage::$includeAllAttrsLabel);
                $I->wait(1);
            }
            foreach ($attrConfig->categories as $categoryId) {
                $I->waitForElementAndClick(sprintf(ListGenPage::$attrTabFormatString, $categoryId->value));
                $I->waitForElementAndClick(sprintf(ListGenPage::$attrToggleFormatString, $categoryId->value));
            }

        }
        $I->waitForElementAndClick(ListGenPage::$attrPageNextStepBtn);
        $I->wait(2);

        // 6. Product Overview
        $I->click(ListGenPage::$overviewPageNextStepBtn);
        $I->wait(2);

        // 7. Choose Project / Save Persona
        $I->fillField("//input[@name='newListName']", $listName);
        $I->chooseProjectStep($projectType, $projectName);

        // 8. Authorize Match Credit Deduction
        $I->waitForElementAndClick(ListGenPage::$authorizeDeducationLabel);
        $I->waitForElementAndClick(ListGenPage::$orderListButton);
    }

    public function doEmailValidationFlow(
        ImportListType $listImportType,
        $listName,
        $projectName,
        ProjectType $projectType,
        bool $alternativeEmails = false
    )
    {
        $I = $this;

        $I->importListStep($listImportType, $listName, $projectName);

        $I->chooseProjectStep($projectType, $projectName, false, $alternativeEmails);
    }

    public function doDataPrepFlow(
        ImportListType $listImportType,
        $listName,
        $projectName,
        ProjectType $projectType,
    )
    {
        $I = $this;

        # Click Data Hygene Tab & Select Data Prep
        $I->waitForElementAndClick(DashboardPage::$dataHygieneTab);
        $I->waitForElementAndClick(DashboardPage::$dataPrepCard);

        // TODO: blocked on this.
        # 0. Intro Step
        // if ($I->elementIsPresent(DataPrep::$hideIntroLabel)) {

        // }
        // $I->Pause();

        // $headerText = $I->grabTextFrom(ListGenPage::$step1Header);
        // if ($headerText === 'What is Data Prep?') {
        //     $I->waitForElementAndClick(ListGenPage::$skipIntroLabel);
        //     $I->waitForElementAndClick(ListGenPage::$nextStepBtn);
        // }

        # Import List & Map Fields
        $I->importListStep($listImportType, $listName);

        # Diagnosis Step
        $I->wait(1);
        $I->waitForElementAndClick(DataPrep::$diagnosisPageNextStep);

        # Data Workbench
        $I->wait(1);
        $I->click("//div[@id='vs-dataprep-input-field-0']"); // click "first input"
        $I->wait(1);
        $I->click("//div[text()='Full Name']"); // click "Full Name option" (Problem: There are 2 of these...)
        $I->wait(1);
        $I->click("//div[@id='vs-dataprep-selected-action']"); // click "second input"
        $I->wait(1);
        $I->click("//div[text()='Split Full Name']"); // click "Split Full Name option"
        $I->wait(1);
        $I->click("//button[text()='Apply Action']"); // click "Apply Action button"
        $I->wait(1);
        $I->click("//div[@id='vs-wizard__step--4']//button[@type='submit']");

        # Export Configuration Step
        $I->wait(1);
        $I->click("//div[@id='vs-wizard__step--5']//button[@type='submit']");

        # Choose Project
        $I->chooseProjectStep($projectType, $projectName);
    }


    // TODO: this just doesnt work
    /**
     * If element is found return true if not return false
     * @param $element
     * @return bool
     */
    // public function elementIsPresent($element)
    // {
    //     $I = $this;

    //     try {
    //         $r = $I->canSeeElement($element);
    //         $isFound = true;
    //         codecept_debug($r); // $r null when elem not there.
    //         codecept_debug("Element found: " . $element);
    //     } catch (\PHPUnit_Framework_ExpectationFailedException $e) {
    //         $isFound = false;
    //         codecept_debug("Element not found: " . $element);
    //     }
    //     return $isFound;
    // }

    private function importListStep(
        ImportListType $importType,
        $listName,
        $projectName = null,
        $excludeFields = false
    )
    {
        $I = $this;
        switch ($importType) {
            case ImportListType::NewList:
                $I->waitForElement(ListInsightsPage::$listInput);
                $I->attachFile(ListInsightsPage::$listInput, $listName);
                $I->waitForText(ListInsightsPage::$uploadCompleteString, 60);
                $I->waitForElementAndClick(ListInsightsPage::$importListNextStepButton);
                // map fields
                $I->wait(1);
                $I->waitForText($listName);
                if ($excludeFields) {
                    $I->waitForElement("//h3[text()='Map Fields']", 60);
                    $I->selectOption("//select[@name='7']", ['text' => 'Ignore Field']); //city
                    $I->selectOption("//select[@name='8']", ['text' => 'Ignore Field']); //state
                }
                $I->waitForElementAndClick(ListInsightsPage::$mapFieldsNextStepButton, 120); // "mapping fields" step can take a while
                break;
            case ImportListType::HubspotList:
                $I->waitForElementAndClick(ListInsightsPage::$importFromHubspotLink);
                $I->wait(2);
                $I->selectOption(ListInsightsPage::$hubspotListSelect, basename($listName, '.csv'));
                $I->waitForText('Finished and ready to be used.');
                $I->waitForElementAndClick(ListInsightsPage::$importFromHubspotNextStepBtn, 60);
                break;
            case ImportListType::ExistingList:
                $I->waitForElementAndClick(ListInsightsPage::$useExistingListCard);
                $I->wait(2);
                $I->selectOption(ListInsightsPage::$projectSelect, $projectName);
                $I->wait(2);
                $I->selectOption(ListInsightsPage::$listNameSelect, $listName);
                $I->waitForElementAndClick(ListInsightsPage::$useExistingListNextStepButton);
                break;
        }
    }

    private function chooseProjectStep(
        ProjectType $projectType,
        $projectName,
        bool $authorizeDeduction = false,
        ?bool $alternativeEmails = null
    )
    {
        $I = $this;
        switch($projectType) {
            case ProjectType::NewProject:
                $I->wait(1);
                $I->pressKey(
                    ChooseProjectPage::$projectNameInput,
                    $projectName,
                    \Facebook\WebDriver\WebDriverKeys::ENTER
                );
                break;
            case ProjectType::ExistingProject:
                $I->wait(2);
                $I->waitForElementAndClick(ChooseProjectPage::$projectNameInputDropDownBtn);
                $projectOptionElem = sprintf(ChooseProjectPage::$projectNameOptionFormatString, $projectName);
                $I->click($projectOptionElem);
                break;
            case ProjectType::PreSelectedProject:
                $I->wait(2);
                $projectNameFromPage = $I->grabTextFrom(ChooseProjectPage::$selectedProjectNameElem);
                $I->assertEquals($projectName, $projectNameFromPage, 'Project name is not pre-selected');
                break;
        }
        $I->waitForElementAndClick(ListInsightsPage::$createButton);

        // Authorize Deduction: ListGen Flow
        if ($authorizeDeduction) {
            $I->waitForElementAndClick(ListGenPage::$authorizeDeducationLabel);
            $I->waitForElementAndClick(ListGenPage::$orderListButton);
        }
        // Authorize Deduction: Email Validation Flow
        if (isset($alternativeEmails)) {
            if ($alternativeEmails) {
                $I->wait(1);
                $I->waitForElementAndClick(EmailValidationPage::$includeAltEmailsLabel);
                $I->assertEquals(
                    $I->grabTextFrom("//label[@for='vs-ev-debit-agree']//p[1]"), //first paragraph of label
                    EmailValidationPage::$authorizeDeducationWithAltsLabelText,
                    'Include Alt emails: label is wrong'
                );
            } else {
                $I->wait(1);
                $I->assertEquals(
                    $I->grabTextFrom("//label[@for='vs-ev-debit-agree']"), // full label
                    EmailValidationPage::$authorizeDeducationLabelText,
                    'Dont include Alt emails: label is wrong'
                );
            }
            // click the checkbox & Order List
            $I->waitForElementAndClick(EmailValidationPage::$authorizeDeducationLabel);
            $I->waitForElementAndClick(EmailValidationPage::$orderListButton);
        }
    }

    public function scrollSettingsPage($x, $y) {
        $I = $this;
        $I->executeJS("document.getElementsByClassName('vs-page-container flex-fill d-none d-md-flex flex-column')[0].scrollBy(arguments[0], arguments[1]);", [$x, $y]);
    }

    private function scrollInsightsPage($x, $y) {
        $I = $this;
        $I->executeJS("document.getElementsByClassName('vs-list-info-pane bg-light w-100 ')[0].scrollBy(arguments[0], arguments[1]);", [$x, $y]);
    }

    public function scrollB2CListGen($x, $y) {
        $I = $this;
        $I->executeJS("document.getElementsByClassName('vs-filters-and-preview__filters px-2 pb-2')[0].scrollBy(arguments[0], arguments[1]);", [$x, $y]);
    }

    public function scrollC2bPage($x, $y) {
        $I = $this;
        $I->executeJS("document.getElementsByClassName('vs-page-container flex-fill d-none d-md-flex flex-column')[0].scrollBy(arguments[0], arguments[1]);", [$x, $y]);
    }

    public function scrollProjectLists()
    {
        $I = $this;
        $I->executeJS("document.getElementById('vs-list-manager__list-cards').scrollBy(0, 2000);");
    }

    public function validateVisualizations($flowType)
    {
        $I = $this;

        if ($flowType == $I::B2B) {
            $I->wait(1);
            $I->canSeeElements(VisualizationPage::getB2BCardHeaders1());

            $I->scrollInsightsPage(0, 2000);
            $I->wait(5);

            $I->canSeeElements(VisualizationPage::getB2BCardHeaders2());

            $I->scrollInsightsPage(0, 2000);
            $I->wait(5);

            $I->canSeeElements(VisualizationPage::getB2CCards());
        }
        else if ($flowType == $I::B2C) {
            $I->wait(2);
            $I->canSeeElements(VisualizationPage::getB2CCards());
        }
    }

    public function exportList(AcceptanceTester $I, $exportType)
    {
        /* Handle different "Export" scenarios */

        // Subscription: B2C: (Contact Append/Demo Append/List Gen)
        if ($exportType == 'b2c-subscription') {
            $I->waitForElementAndClick(VisualizationPage::$exportBtn);
            $I->waitForElementAndClick(VisualizationPage::$termsOfServiceAgreeBtn);
            $I->wait(3);
            return;
        }

        // Subscription: B2B: (AMB/Persona/Lookalike)
        if ($exportType == 'b2b-subscription') {
            $I->waitForElementAndClick(VisualizationPage::$exportBtn);
            $I->wait(3);
        }

        // Subscription: B2B: Online Audience
        if ($exportType == 'b2b-subscription-oa') {
            $I->waitForElementAndClick("//div[@id='export-button']//button");
            $I->waitForElementAndClick(VisualizationPage::$exportCheckbox);
            $I->waitForElementAndClick(VisualizationPage::$confirmExportBtn);
            $I->wait(3);
            return;
        }

        // Subscription: B2C: Online Audience
        if ($exportType == 'b2c-subscription-oa') {
            $I->waitForElementAndClick("//div[@id='export-button']//button");
            $I->waitForElementAndClick(VisualizationPage::$exportCheckbox);
            $I->waitForElementAndClick(VisualizationPage::$confirmExportBtn);
            $I->waitForElementAndClick(VisualizationPage::$termsOfServiceAgreeBtn);
            $I->wait(3);
            return;
        }
    }

    public function dismissAppVersionToast(AcceptanceTester $I) {
        $I->waitForElementAndClick(DashboardPage::$appVersionToast, 5);
        $I->wait(1);
    }

    /**
     * The Marketing Star Modal can be dismissed by setting marketingStarPromoModalComplete => true in localStorage
     * OR by setting marketingStarPromoModalClosed => true in sessionStorage
     */
    public function setMarketingStarModalCookie()
    {
        $I = $this;
        $I->executeJS("localStorage.setItem('marketingStarPromoModalComplete', 'true');");
    }

    /**
     * Check for presence of Marketing Star Modal and dismiss it if found
     * (The preferred method is to use setMarketingStarModalCookie())
     */
    public function dismissMarketingStarModal()
    {
        $I = $this;

        if ($I->elementIsPresent(DashboardPage::$MSCloseModalBtn)) {
            $I->waitForElementAndClick(DashboardPage::$MSCloseModalBtn);
            $I->wait(1);
        }
    }




    public function setAlertBannerCookie()
    {
        $I = $this;
        $I->setCookie(
            "alertBannerCookie",
            "Try%20our%20new%20and%20improved%20Consumer%20Audience%20Builder%20product%2C%20now%20with%20more%20than%2075%20filters!"
        );
    }


    public function _goToDataPrepWorkArea(ImportListType $listImportType, $listName)
    {
        $I = $this;

         #click dataHygene tab
         $I->waitForElementAndClick(DashboardPage::$dataHygieneTab);
         #click dataPrep
         $I->waitForElementAndClick(DashboardPage::$dataPrepCard);
         #import list
         $I->importListStep($listImportType, $listName);
         #diagnosis step; click next
         $I->wait(1);
         $I->waitForElementAndClick("//div[@id='vs-wizard__step--3']//button[@type='submit']");
         $I->wait(1);
    }
}
