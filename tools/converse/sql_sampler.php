<?php

// open ssh tunnel to db server & forward sql port before running!
//
// Usage: php sql_sampler.php [modulus] [targetBucket] [sampleSize]
// Arguments:
//   modulus      - Modulus value for sampling (default: 1000)
//   targetBucket - Target bucket for sampling (default: 1)
//   sampleSize   - Maximum number of rows to return (default: 5000)
//
// Example: php sql_sampler.php 1000 1 5000


// load env
require_once __DIR__ . '/../../vendor/autoload.php';
use Symfony\Component\Dotenv\Dotenv;
$dotenv = new Dotenv();
$dotenv->load(__DIR__ . '/../../.env');


// Parse command line arguments with defaults
$modulus = isset($argv[1]) ? (int)$argv[1] : 1000;
$targetBucket = isset($argv[2]) ? (int)$argv[2] : 1;
$sampleSize = isset($argv[3]) ? (int)$argv[3] : 5000;

// Validate arguments
if ($modulus <= 0) {
    echo "Error: modulus must be greater than 0\n";
    exit(1);
}
if ($targetBucket < 0 || $targetBucket >= $modulus) {
    echo "Error: targetBucket must be between 0 and " . ($modulus - 1) . "\n";
    exit(1);
}
if ($sampleSize <= 0) {
    echo "Error: sampleSize must be greater than 0\n";
    exit(1);
}

echo "SQL Sampler: Using parameters: modulus=$modulus, targetBucket=$targetBucket, sampleSize=$sampleSize\n";


// database access
$host = '127.0.0.1';
$port = '3307';
$dbname = $_ENV['DB_NAME'];
$user = $_ENV['DB_USER'];
$password = $_ENV['DB_PASSWORD'];

// output
$fileName = "cv_slice_$sampleSize.csv";
$outputDir = __DIR__ . '/samples/';
$outputFile = $outputDir . $fileName;

try {
    // Set up PDO connection
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $user, $password);

    // Set error mode
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Prepare query
    $sql = "
        SELECT *
        FROM Converse
        WHERE MOD(CONV(SUBSTRING(MD5(`STHHLD`), 1, 8), 16, 10), $modulus) = $targetBucket
        LIMIT $sampleSize
    ";


    echo "SQL Sampler: Executing query: $sql\n";

    // Execute query
    $start = microtime(true);
    $stmt = $pdo->query($sql);
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($rows)) {
        echo "No rows returned.\n";
        exit;
    }

    $end = microtime(true);
    $time = $end - $start;
    echo "SQL Sampler: SQL Query Completed in $time seconds.\n";

    // Open CSV file for writing
    $fp = fopen($outputFile, 'w');

    // Write header
    fputcsv($fp, array_keys($rows[0]));

    // Write each row
    foreach ($rows as $row) {
        fputcsv($fp, $row);
    }

    fclose($fp);

    echo "SQL Sampler: Sample exported to $outputFile\n";

} catch (PDOException $e) {
    echo "SQL Sampler: Database error: " . $e->getMessage();
    exit;
}
