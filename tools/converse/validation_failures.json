[{"column": "Zip", "value": "602", "pattern": "/^\\d{4,8}$/", "row_num": 15, "STHHLD": "00602:GOMEZ::304063HC"}, {"column": "Zip", "value": "911", "pattern": "/^\\d{4,8}$/", "row_num": 15, "STHHLD": "00911:KING::13CALLEE"}, {"column": "Zip", "value": "911", "pattern": "/^\\d{4,8}$/", "row_num": 15, "STHHLD": "00911:KING::13CALLEE"}, {"column": "EmailAddr", "value": "nana45marie@aol. com", "pattern": "/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$/", "row_num": 401, "STHHLD": "02145:HAMMERSMITH::HAMPSHIR"}, {"column": "STHHLD", "value": "02364:CANNATA3RD::SYCAMORE", "pattern": "/^\\d{5}:[A-Za-z]+::[A-Za-z0-9]+$/", "row_num": 5, "STHHLD": "02364:CANNATA3RD::SYCAMORE"}, {"column": "ST10", "value": "02364:CANNATA3RD:EDW:SYCAMORE", "pattern": "/^\\d{5}:[A-Za-z]+:[A-Za-z]+:[A-Za-z0-9]+$/", "row_num": 7, "STHHLD": "02364:CANNATA3RD::SYCAMORE"}, {"column": "LastName", "value": "CANNATA 3RD", "pattern": "/^[A-Za-z\\s'-]+$/", "row_num": 10, "STHHLD": "02364:CANNATA3RD::SYCAMORE"}, {"column": "EmailAddr", "value": "UNA.GIHMARTIN @GMAIL.COM", "pattern": "/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$/", "row_num": 401, "STHHLD": "07030:GILMARTIN::8324FWIL"}]