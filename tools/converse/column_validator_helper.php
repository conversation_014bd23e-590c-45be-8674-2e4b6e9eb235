<?php

$columnValueTests = [
    # 0
    "SKID" => [
        'pattern' => '/^\d{5}_\d{3}$/',
        'canBeEmpty' => false,
    ],
    # 1
    "ADDRID" => [
        'pattern' => '/^0$/',
        'canBeEmpty' => false,
    ],
    # 2
    "STADDR" => [
        'pattern' => '/^\d{5}:::[A-Za-z0-9]+$/',
        'canBeEmpty' => false,
    ],
    # 3
    "HHLDID" => [
        'pattern' => '/^0$/',
        'canBeEmpty' => false,
    ],
    # 4
    "STHHLD" => [
        'pattern' => '/^\d{5}:[A-Za-z]+::[A-Za-z0-9]+$/',
        'canBeEmpty' => false,
    ],
    # 5
    "INDIVID" => [
        'pattern' => '/^0$/',
        'canBeEmpty' => false,
    ],
    # 6
    "ST10" => [
        'pattern' => '/^\d{5}:[A-Za-z]+:[A-Za-z]+:[A-Za-z0-9]+$/',
        'canBeEmpty' => false,
    ],
    # 7
    "FirstName" => [
        'pattern' => '/^[A-Za-z\s\'-]+$/',
        'canBeEmpty' => false,
    ],
    # 8
    "MiddleName" => [
        'pattern' => '/^[A-Z]$/',
        'canBeEmpty' => true,
    ],
    # 9
    "LastName" => [
        'pattern' => '/^[A-Za-z\s\'-]+$/',
        'canBeEmpty' => false,
    ],
    # 10
    "Address" => [
        'pattern' => '/^[A-Za-z0-9\s\'\/#-]+$/',
        'canBeEmpty' => true,
    ],
    # 11
    "Unit" => [
        'pattern' => '/^[A-Za-z0-9\s\'\/#-]+$/',
        'canBeEmpty' => true,
    ],
    # 12
    "City" => [
        'pattern' => '/^[A-Za-z\s\'-]+$/',
        'canBeEmpty' => true,
    ],
    # 13
    "State" => [
        'pattern' => '/^[A-Z]{2}$/',
        'canBeEmpty' => true,
    ],
    # 14
    "Zip" => [
        'pattern' => '/^\d{4,8}$/',
        'canBeEmpty' => true,
    ],
    # 15
    "Zip4" => [
        'pattern' => '/^\d{4}$/',
        'canBeEmpty' => true,
    ],
    # 16
    "AddrTypeIndicator" => [ //no values for this column
        'pattern' => null,
        'canBeEmpty' => true,
    ],
    # 17
    "CensusMedianHomeValue" => [
        'pattern' => '/^\d{1,5}$/',
        'canBeEmpty' => false,
    ],
    # 18
    "CensusMedianHouseholdIncome" => [
        'pattern' => '/^\d{1,4}$/',
        'canBeEmpty' => false,
    ],
    # 19
    "NumSrc" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
    ],
    # 20
    "FileDate" => [
        'pattern' => '/^\d{8}$/',
        'canBeEmpty' => false,
    ],
    # 21
    "BaseRecVerificationDate" => [ //no values for this column
        'pattern' => null,
        'canBeEmpty' => true,
    ],
    # 22
    "DOB" => [
        'pattern' => '/^\d{8}$/',
        'canBeEmpty' => true,
    ],
    # 23
    "Phone" => [
        'pattern' => '/^\d{8,10}$/',
        'canBeEmpty' => true,
    ],
    # 24
    "GenderCode" => [
        'pattern' => '/^[MF]$/',
        'canBeEmpty' => true,
    ],
    # 25
    "InferredHouseholdRank" => [
        'pattern' => '/^[1-9]$/',
        'canBeEmpty' => true,
    ],
    # 26
    "EstimatedHouseholdIncome" => [
        'pattern' => '/^[A-S]$/',
        'canBeEmpty' => true,
    ],
    # 27
    "NetWorth" => [
        'pattern' => '/^[A-I]$/',
        'canBeEmpty' => true,
    ],
    # 28
    "NumberCreditLines" => [
        'pattern' => '/^[1-9]$/',
        'canBeEmpty' => true,
    ],
    # 29
    "RangeOfNewCredit" => [
        'pattern' => '/^[1-7]$/',
        'canBeEmpty' => true,
    ],
    # 30
    "Education" => [
        'pattern' => '/^[A-D]$/',
        'canBeEmpty' => true,
    ],
    # 31
    "Occupation" => [
        'pattern' => '/^[A-Z]$/',
        'canBeEmpty' => false,
    ],
    # 32
    "OccupationDetail" => [
        'pattern' => '/^[A-Z0-9]{4}$/',
        'canBeEmpty' => false,
    ],
    # 33
    "BusinessOwner" => [
        'pattern' => '/^[1-9,A]$/',
        'canBeEmpty' => true,
    ],
    # 34
    "NumberOfChildren" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => true,
    ],
    # 35
    "PresenceOfChildren" => [
        'pattern' => '/^[Y,N]$/',
        'canBeEmpty' => true,
    ],
    # 36
    "MaritalStatusInHousehold" => [
        'pattern' => '/^[SMAB]$/',
        'canBeEmpty' => true,
    ],
    # 37
    "HomeOwnerRenter" => [
        'pattern' => '/^[H,R,9]$/',
        'canBeEmpty' => true,
    ],
    # 38
    "LengthOfResidence" => [
        'pattern' => '/^\d{1,2}$/',
        'canBeEmpty' => false,
    ],
    # 39
    "DwellingType" => [
        'pattern' => '/^[S,M]$/',
        'canBeEmpty' => true,
    ],
    # 40
    "NumberOfAdults" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 41
    "HouseholdSize" => [
        'pattern' => '/^\d{1}$/',
        'canBeEmpty' => false,
    ],
    # 42
    "HomeMarketValue" => [
        'pattern' => '/^\d{1,7}$/',
        'canBeEmpty' => false,
    ],
    # 43
    "GenerationsInHousehold" => [
        'pattern' => '/^[1-4]$/',
        'canBeEmpty' => true,
    ],
    # 44
    "MailOrderBuyer" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 45
    "MailOrderResponder" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 46
    "OnlinePurchasingIndicator" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 47
    "MembershipClubs" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 48
    "ValuePriceGeneralMerchandiseBuyers" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 49
    "ApparelWomens" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 50
    "ApparelWomensPetite" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 51
    "ApparelWomensPlusSize" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 52
    "ApparelWomensYoung" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 53
    "ApparelMens" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 54
    "ApparelMensBigAndTall" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 55
    "ApparelMensYoung" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 56
    "ApparelChildrens" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 57
    "HealthAndBeauty" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 58
    "BeautyCosmetics" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 59
    "Jewelry" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 60
    "Luggage" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 61
    "CardHolderAmericanExpressGoldPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 62
    "CardHolderAmericanExpressRegular" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 63
    "CardHolderDiscoverGoldPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 64
    "CardHolderDiscoverRegular" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 65
    "CardHolderGasolineRetailGoldPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 66
    "CardHolderGasolineRetailRegular" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 67
    "CardHolderMastercardGoldPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 68
    "CardHolderMastercardRegular" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 69
    "CardHolderVisaGoldPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 70
    "CardHolderVisaRegular" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 71
    "CardHolderBank" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 72
    "CardHolderGasDeptRetail" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 73
    "CardHolderTravelEntertainment" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 74
    "CardHolderUnknownType" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 75
    "CardHolderPremium" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 76
    "CardHolderUpscale" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 77
    "CreditCardUser" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 108
    "SingleParent" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 208
    "TravelDomestic" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 308
    "Females3_5" => [
        'pattern' => '/^[Y]$/',
        'canBeEmpty' => true,
    ],
    # 400
    "EmailAddr" => [
        'pattern' => '/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/',
        'canBeEmpty' => true,
    ],
    # 431
    "MobilePhone" => [
        'pattern' => '/^\d{8,10}$/',
        'canBeEmpty' => true,
    ],
    # 432
    "MobileLineType" => [
        'pattern' => '/^[BLVW]$/',
        'canBeEmpty' => true,
    ],
    # 433
    "MobileCarrier" => [
        'pattern' => '/^[A-Za-z0-9&-.\/\s]{3,}$/',
        'canBeEmpty' => true,
    ],
    # 451
    "CVGroupAddress" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 452
    "CVGroupPhone" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 453
    "CVGroupEmailAddr" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 454
    "CVGroupNamePhone" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 455
    "CVGroupNameEmailAddr" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 456
    "CVGroupNameAddress" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 457
    "CVGroupMobilePhone" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 458
    "CVGroupMobilePhoneName" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 459
    "PhoneLineType" => [
        'pattern' => '/^[BLVW]$/',
        'canBeEmpty' => true,
    ],
    # 460
    "PhoneCarrier" => [
        'pattern' => '/^[A-Za-z0-9&-.\/\s]{3,}$/',
        'canBeEmpty' => true,
    ],
    # 461
    "MobilePhoneLineType" => [
        'pattern' => '/^[BLVW]$/',
        'canBeEmpty' => true,
    ],
    # 465
    "PROP_AreaBuilding" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 466
    "PROP_AreaFirstFloor" => [
        'pattern' => '/^\d+$/',
        'canBeEmpty' => false,
    ],
    # 483
    "AUDEX_DevIDPIPE" => [
        'pattern' => '/^[A-Z0-9-|]+$/',
        'canBeEmpty' => true,
    ],
    # 484
    "AUDEX_EmailAddrPIPE" => [
        'pattern' => '/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}|$/',
        'canBeEmpty' => true,
    ],
    # 485
    "AUDEX_PhonePIPE" => [
        'pattern' => '/^\d{8,10}|$/',
        'canBeEmpty' => true,
    ],
];
