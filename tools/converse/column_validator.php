<?php

include_once 'column_validator_helper.php';


# read in the sample data
$path = './samples/cv_slice_5000.csv';
$fp = fopen($path, 'r');
$headerRow = fgetcsv($fp);

$failures = [];

# TODO: Complete all 485 column tests :(
# TODO: Check that the length of headerRow matches the length of $columnValueTests,
#   if they don't match, throw an error for unexpected/missing columns.
# TODO: Determin the best format for reporting the failures.
# TODO: Track column level stats, like
    # null % rate
    # regex match rate
    # common failure values
# TODO: Check referential integrity? (zip is in State; city is in state)

while (($rec = fgetcsv($fp)) !== false) {

    $fullRecord = array_combine($headerRow, $rec);

    # iterate through column headers
    foreach ($headerRow as $columnHeader) {

        # if a test exists for the column, perform it
        if (array_key_exists($columnHeader, $columnValueTests)) {
            $test = $columnValueTests[$columnHeader];
            $value = $fullRecord[$columnHeader];

            if ($test['canBeEmpty'] && empty($value)) {
                continue;
            }

            if (!preg_match($test['pattern'], $value)) {
                echo "Failed value test for $columnHeader with value $value \n";
                $failures[] = [
                    'column' => $columnHeader,
                    'value' => $value,
                    'pattern' => $test['pattern'],
                    'STHHLD' => $fullRecord['STHHLD'],
                ];
            }
        }
    }
}

file_put_contents('validation_failures.json', json_encode($failures, JSON_PRETTY_PRINT));

echo "Tests complete.\n";